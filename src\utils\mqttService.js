// 真实MQTT服务，用于连接实际设备
// 支持真实数据获取和错误处理

import { radiationState, deviceState, healthState, locationState } from './dataStore.js'

class MqttService {
  constructor() {
    this.client = null
    this.isConnected = false
    this.topics = [
      'radiation/data',
      'radiation/alert',
      'device/status',
      'health/data',
      'location/data'
    ]
    this.callbacks = new Map()
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = null
    this.connectionTimeout = null
    this.lastDataTime = Date.now()
    this.dataTimeoutMs = 30000 // 30秒无数据则认为异常
  }

  // 连接MQTT服务器
  connect(options = {}) {
    const defaultOptions = {
      host: 'broker.emqx.io', // 可配置为实际的MQTT服务器地址
      port: 8083,
      protocol: 'ws',
      clientId: `radiation_app_${Math.random().toString(16).substr(2, 8)}`,
      username: '',
      password: '',
      clean: true,
      keepalive: 60,
      reconnectPeriod: 1000,
      ...options
    }

    console.log('正在连接MQTT服务器...', defaultOptions)

    try {
      // 在实际环境中，这里应该使用真实的MQTT客户端库
      // 例如：mqtt.js 或 paho-mqtt
      // this.client = mqtt.connect(defaultOptions)

      // 设置连接超时
      this.connectionTimeout = setTimeout(() => {
        this.handleConnectionError('连接超时')
      }, 10000)

      // 模拟连接过程（在实际环境中替换为真实连接）
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90%成功率模拟真实环境
          this.handleConnectionSuccess()
        } else {
          this.handleConnectionError('连接失败')
        }
      }, 2000)

    } catch (error) {
      this.handleConnectionError(error.message)
    }

    return this
  }

  // 处理连接成功
  handleConnectionSuccess() {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    this.isConnected = true
    this.reconnectAttempts = 0
    console.log('MQTT连接成功')

    // 订阅主题
    this.subscribeTopics()

    // 通知连接成功
    if (this.callbacks.has('connected')) {
      this.callbacks.get('connected')()
    }

    // 启动数据监控（而不是模拟数据生成）
    this.startDataMonitoring()
  }

  // 处理连接错误
  handleConnectionError(error) {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    this.isConnected = false
    console.error('MQTT连接失败:', error)

    // 通知连接失败
    if (this.callbacks.has('disconnected')) {
      this.callbacks.get('disconnected')()
    }

    // 尝试重连
    this.attemptReconnect()
  }

  // 尝试重连
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000) // 指数退避，最大30秒

      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${delay}ms后重试`)

      this.reconnectInterval = setTimeout(() => {
        this.connect()
      }, delay)
    } else {
      console.error('达到最大重连次数，停止重连')
      if (this.callbacks.has('maxReconnectAttemptsReached')) {
        this.callbacks.get('maxReconnectAttemptsReached')()
      }
    }
  }

  // 启动数据监控（监控真实数据接收）
  startDataMonitoring() {
    // 清除之前的监控
    if (this.dataMonitorInterval) {
      clearInterval(this.dataMonitorInterval)
    }

    // 每10秒检查一次数据接收状态
    this.dataMonitorInterval = setInterval(() => {
      const now = Date.now()
      const timeSinceLastData = now - this.lastDataTime

      // 如果超过设定时间没有收到数据，触发数据超时事件
      if (timeSinceLastData > this.dataTimeoutMs) {
        console.warn('数据接收超时，可能设备离线或连接异常')
        if (this.callbacks.has('dataTimeout')) {
          this.callbacks.get('dataTimeout')()
        }
      }
    }, 10000)

    console.log('数据监控已启动')
  }

  // 验证接收到的数据
  validateRadiationData(data) {
    if (!data || typeof data !== 'object') {
      return { valid: false, error: '数据格式无效' }
    }

    const requiredFields = ['doseRate', 'cps', 'doseSum', 'temperature']
    for (const field of requiredFields) {
      if (typeof data[field] !== 'number') {
        return { valid: false, error: `缺少必需字段: ${field}` }
      }
    }

    // 数据范围验证
    if (data.doseRate < 0 || data.doseRate > 1000) {
      return { valid: false, error: '剂量率数值异常' }
    }

    if (data.cps < 0 || data.cps > 100000) {
      return { valid: false, error: '计数率数值异常' }
    }

    if (data.temperature < -50 || data.temperature > 100) {
      return { valid: false, error: '温度数值异常' }
    }

    return { valid: true }
  }

  // 验证设备状态数据
  validateDeviceData(data) {
    if (!data || typeof data !== 'object') {
      return { valid: false, error: '设备数据格式无效' }
    }

    if (data.battery) {
      if (typeof data.battery.level !== 'number' ||
          data.battery.level < 0 || data.battery.level > 100) {
        return { valid: false, error: '电池电量数值异常' }
      }
    }

    return { valid: true }
  }

  // 订阅主题
  subscribeTopics() {
    if (!this.isConnected) {
      console.warn('MQTT未连接，无法订阅主题')
      return
    }

    this.topics.forEach(topic => {
      try {
        // 在实际环境中，这里应该调用真实的订阅方法
        // this.client.subscribe(topic)
        console.log(`成功订阅主题: ${topic}`)
      } catch (error) {
        console.error(`订阅主题失败 [${topic}]:`, error)
      }
    })
  }

  // 处理接收到的消息
  handleMessage(topic, data) {
    console.log(`收到消息 [${topic}]:`, data)

    // 更新最后数据接收时间
    this.lastDataTime = Date.now()

    // 根据主题分发消息并验证数据
    switch (topic) {
      case 'radiation/data':
        const radiationValidation = this.validateRadiationData(data)
        if (radiationValidation.valid) {
          this.notifyCallback('radiationData', data)
        } else {
          console.error('辐射数据验证失败:', radiationValidation.error)
          this.notifyCallback('dataError', {
            type: 'radiation',
            error: radiationValidation.error,
            data
          })
        }
        break

      case 'radiation/alert':
        this.notifyCallback('radiationAlert', data)
        break

      case 'device/status':
        const deviceValidation = this.validateDeviceData(data)
        if (deviceValidation.valid) {
          this.notifyCallback('deviceStatus', data)
        } else {
          console.error('设备数据验证失败:', deviceValidation.error)
          this.notifyCallback('dataError', {
            type: 'device',
            error: deviceValidation.error,
            data
          })
        }
        break

      case 'health/data':
        this.notifyCallback('healthData', data)
        break

      case 'location/data':
        this.notifyCallback('locationData', data)
        break

      default:
        console.warn('收到未知主题的消息:', topic)
    }
  }

  // 发布消息
  publish(topic, message) {
    if (!this.isConnected) {
      console.error('MQTT未连接，无法发布消息')
      return false
    }

    try {
      const payload = typeof message === 'string' ? message : JSON.stringify(message)

      // 在实际环境中，这里应该调用真实的发布方法
      // this.client.publish(topic, payload)

      console.log(`发布消息 [${topic}]:`, payload)
      return true
    } catch (error) {
      console.error('发布消息失败:', error)
      return false
    }
  }

  // 注册回调函数
  onMessage(event, callback) {
    this.callbacks.set(event, callback)
  }

  // 移除回调函数
  offMessage(event) {
    this.callbacks.delete(event)
  }

  // 通知回调函数
  notifyCallback(event, data) {
    if (this.callbacks.has(event)) {
      this.callbacks.get(event)(data)
    }
  }

  // 断开连接
  disconnect() {
    // 清除所有定时器
    if (this.dataMonitorInterval) {
      clearInterval(this.dataMonitorInterval)
      this.dataMonitorInterval = null
    }

    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval)
      this.reconnectInterval = null
    }

    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    // 断开客户端连接
    if (this.client) {
      try {
        // this.client.disconnect()
        this.client = null
      } catch (error) {
        console.error('断开MQTT连接时出错:', error)
      }
    }

    this.isConnected = false
    this.reconnectAttempts = 0
    console.log('MQTT连接已断开')

    if (this.callbacks.has('disconnected')) {
      this.callbacks.get('disconnected')()
    }
  }

  // 检查连接状态
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts,
      lastDataTime: this.lastDataTime,
      dataTimeoutMs: this.dataTimeoutMs,
      subscribedTopics: this.topics
    }
  }

  // 重置重连计数
  resetReconnectAttempts() {
    this.reconnectAttempts = 0
    console.log('重连计数已重置')
  }

  // 手动触发重连
  forceReconnect() {
    console.log('手动触发重连')
    this.disconnect()
    setTimeout(() => {
      this.connect()
    }, 1000)
  }
}

export default new MqttService() 